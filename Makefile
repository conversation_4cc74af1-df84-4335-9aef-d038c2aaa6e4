.PHONY: build clean scheduler consumer deps

# Build all binaries
build: deps scheduler consumer

# Install dependencies
deps:
	go mod tidy

# Build scheduler
scheduler:
	go build -o bin/scheduler cmd/scheduler/main.go

# Build consumer
consumer:
	go build -o bin/consumer cmd/consumer/main.go

# Clean build artifacts
clean:
	rm -rf bin/

# Run scheduler
run-scheduler: scheduler
	./bin/scheduler

# Run consumer
run-consumer: consumer
	./bin/consumer

# Help
help:
	@echo "Available targets:"
	@echo "  build         \t- Build all binaries"
	@echo "  scheduler     \t- Build scheduler binary"
	@echo "  consumer      \t- Build consumer binary"
	@echo "  deps          \t- Install dependencies"
	@echo "  clean         \t- Clean build artifacts"
	@echo "  run-scheduler \t- Build and run scheduler"
	@echo "  run-consumer  \t- Build and run consumer"
	@echo "  help          \t- Show this help"
