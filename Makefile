.PHONY: build clean scheduler consumer deps

# Build all binaries
build: deps scheduler consumer

# Install dependencies
deps:
	go mod tidy

# Build scheduler
scheduler:
	go build -o bin/scheduler cmd/scheduler/main.go

# Build consumer
consumer:
	go build -o bin/consumer cmd/consumer/main.go

# Clean build artifacts
clean:
	rm -rf bin/

# Run scheduler
run-scheduler: scheduler
	./bin/scheduler

# Run consumer
run-consumer: consumer
	./bin/consumer

# Help
help:
	@echo "Available targets:"
	@echo "  build        - Build all binaries"
	@echo "  scheduler    - Build scheduler binary"
	@echo "  consumer     - Build consumer binary"
	@echo "  deps         - Install dependencies"
	@echo "  clean        - Clean build artifacts"
	@echo "  run-scheduler - Build and run scheduler"
	@echo "  run-consumer  - Build and run consumer"
	@echo "  help         - Show this help"
