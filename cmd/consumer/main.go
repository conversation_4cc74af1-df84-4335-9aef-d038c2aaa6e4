package main

import (
	"context"
	"fmt"
	"log"
	"os"
	"os/signal"
	"syscall"
	"time"

	"sqs-test/config"
	"sqs-test/utils"

	"github.com/aws/aws-sdk-go-v2/service/sqs"
)

func main() {
	fmt.Println("Starting SQS Consumer Client...")

	// Load configuration
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// Create SQS client
	sqsClient := sqs.NewFromConfig(cfg.AWSConfig)

	// Create message tracker
	tracker := utils.NewMessageTracker()

	// Setup graceful shutdown
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// Handle interrupt signals
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	go func() {
		<-sigChan
		fmt.Println("\nReceived interrupt signal, shutting down...")
		cancel()
	}()

	// Start metrics reporting
	go func() {
		ticker := time.NewTicker(5 * time.Second)
		defer ticker.Stop()

		for {
			select {
			case <-ctx.Done():
				return
			case <-ticker.C:
				tracker.PrintStats()
			}
		}
	}()

	fmt.Printf("Consuming messages from queue: %s\n", cfg.SQSQueueURL)
	fmt.Println("Press Ctrl+C to stop...")

	// Main message processing loop
	for {
		select {
		case <-ctx.Done():
			fmt.Println("Shutting down consumer...")
			tracker.PrintStats()
			return
		default:
			err := processMessages(ctx, sqsClient, cfg.SQSQueueURL, tracker)
			if err != nil {
				log.Printf("Error processing messages: %v", err)
				time.Sleep(1 * time.Second)
			}
		}
	}
}

func processMessages(ctx context.Context, client *sqs.Client, queueURL string, tracker *utils.MessageTracker) error {
	// Receive messages from SQS
	result, err := client.ReceiveMessage(ctx, &sqs.ReceiveMessageInput{
		QueueUrl:            &queueURL,
		MaxNumberOfMessages: 10,
		WaitTimeSeconds:     20, // Long polling
	})
	if err != nil {
		return fmt.Errorf("failed to receive messages: %w", err)
	}

	// Process each message
	for _, message := range result.Messages {
		if message.Body == nil {
			continue
		}

		// Track message and check for duplicates
		isDuplicate := tracker.ProcessMessage(*message.Body)

		if isDuplicate {
			fmt.Printf("DUPLICATE detected: %s\n", *message.Body)
		} else {
			fmt.Printf("NEW message: %s\n", *message.Body)
		}

		// Delete message from queue
		err := deleteMessage(ctx, client, queueURL, message.ReceiptHandle)
		if err != nil {
			log.Printf("Failed to delete message: %v", err)
		}
	}

	return nil
}

func deleteMessage(ctx context.Context, client *sqs.Client, queueURL string, receiptHandle *string) error {
	if receiptHandle == nil {
		return fmt.Errorf("receipt handle is nil")
	}

	_, err := client.DeleteMessage(ctx, &sqs.DeleteMessageInput{
		QueueUrl:      &queueURL,
		ReceiptHandle: receiptHandle,
	})
	return err
}
