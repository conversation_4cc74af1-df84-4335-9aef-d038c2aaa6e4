package main

import (
	"context"
	"fmt"
	"log"
	"os"
	"os/signal"
	"syscall"
	"time"

	"sqs-test/config"
	"sqs-test/utils"

	"github.com/aws/aws-sdk-go-v2/service/sqs"
)

func main() {
	fmt.Println("Starting SQS Consumer Client...")

	// Load configuration
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// Create SQS client
	sqsClient := sqs.NewFromConfig(cfg.AWSConfig)

	// Create message tracker
	tracker := utils.NewMessageTracker()

	// Setup graceful shutdown
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// Handle interrupt signals
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	go func() {
		<-sigChan
		fmt.Println("\nReceived interrupt signal, shutting down...")

		// Give 10 seconds for graceful shutdown
		go func() {
			time.Sleep(10 * time.Second)
			fmt.Println("Shutdown timeout reached, forcing exit...")
			os.Exit(1)
		}()

		cancel()
	}()

	// Start metrics reporting
	go func() {
		ticker := time.NewTicker(5 * time.Second)
		defer ticker.Stop()

		for {
			select {
			case <-ctx.Done():
				return
			case <-ticker.C:
				tracker.PrintStats()
			}
		}
	}()

	fmt.Printf("Consuming messages from queue: %s\n", cfg.SQSQueueURL)
	fmt.Println("Press Ctrl+C to stop...")

	// Main message processing loop
	for {
		select {
		case <-ctx.Done():
			fmt.Println("Shutting down consumer...")
			tracker.PrintStats()
			return
		default:
			err := processMessages(ctx, sqsClient, cfg.SQSQueueURL, tracker)
			if err != nil {
				log.Printf("Error processing messages: %v", err)
				time.Sleep(1 * time.Second)
			}
		}
	}
}

func processMessages(ctx context.Context, client *sqs.Client, queueURL string, tracker *utils.MessageTracker) error {
	// Create timeout context for AWS operations
	opCtx, cancel := context.WithTimeout(ctx, 30*time.Second)
	defer cancel()

	// Receive messages from SQS
	result, err := client.ReceiveMessage(opCtx, &sqs.ReceiveMessageInput{
		QueueUrl:            &queueURL,
		MaxNumberOfMessages: 10,
		WaitTimeSeconds:     20, // Long polling
	})
	if err != nil {
		return fmt.Errorf("failed to receive messages: %w", err)
	}

	// Process each message
	for _, message := range result.Messages {
		if message.MessageId == nil {
			continue
		}

		messageBody := ""
		if message.Body != nil {
			messageBody = *message.Body
		}

		// Check for duplicates WITHOUT marking as processed yet
		isDuplicate := tracker.IsDuplicate(*message.MessageId)

		if isDuplicate {
			fmt.Printf("DUPLICATE detected - ID: %s, Body: %s\n", *message.MessageId, messageBody)
		} else {
			fmt.Printf("NEW message - ID: %s, Body: %s\n", *message.MessageId, messageBody)
		}

		// Delete message from queue first
		err := deleteMessage(opCtx, client, queueURL, message.ReceiptHandle)
		if err != nil {
			log.Printf("Failed to delete message: %v", err)
			// Don't mark as processed if deletion failed
		} else {
			// Only mark as processed after successful deletion
			tracker.MarkAsProcessed(*message.MessageId, isDuplicate)
		}
	}

	return nil
}

func deleteMessage(ctx context.Context, client *sqs.Client, queueURL string, receiptHandle *string) error {
	if receiptHandle == nil {
		return fmt.Errorf("receipt handle is nil")
	}

	_, err := client.DeleteMessage(ctx, &sqs.DeleteMessageInput{
		QueueUrl:      &queueURL,
		ReceiptHandle: receiptHandle,
	})
	return err
}
