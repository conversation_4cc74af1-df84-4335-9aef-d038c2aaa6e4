package main

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"time"

	"sqs-test/config"
	"sqs-test/utils"

	"github.com/aws/aws-sdk-go-v2/service/scheduler"
	"github.com/aws/aws-sdk-go-v2/service/scheduler/types"
)

func main() {
	fmt.Println("Starting EventBridge Scheduler Client...")

	// Load configuration
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// Create scheduler client
	schedulerClient := scheduler.NewFromConfig(cfg.AWSConfig)

	// Calculate start time (20 seconds from now)
	startTime := time.Now().Add(20 * time.Second)
	fmt.Printf("Scheduling 100 messages starting at: %s\n", startTime.Format("15:04:05"))

	// Create 1000 schedules: 100 per second for 10 seconds
	scheduleCount := 0
	successCount := 0
	failureCount := 0

	for second := 0; second < 10; second++ {
		scheduleTime := startTime.Add(time.Duration(second) * time.Second)

		for messageInSecond := 0; messageInSecond < 100; messageInSecond++ {
			scheduleCount++
			scheduleUUID := utils.GenerateUniqueMessage()
			scheduleName := fmt.Sprintf("sqs-test-schedule-%d-%s", scheduleCount, scheduleUUID)

			// Generate unique message
			messageBody := utils.GenerateUniqueMessage()

			// Create SQS message payload
			sqsMessage := map[string]interface{}{
				"QueueUrl":    cfg.SQSQueueURL,
				"MessageBody": messageBody,
			}

			sqsMessageJSON, err := json.Marshal(sqsMessage)
			if err != nil {
				log.Printf("Failed to marshal SQS message: %v", err)
				failureCount++
				continue
			}

			// Create schedule with timeout
			ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
			err = createSchedule(ctx, schedulerClient, scheduleName, scheduleTime, string(sqsMessageJSON), cfg.SchedulerRoleARN)
			cancel()

			if err != nil {
				log.Printf("Failed to create schedule %s: %v", scheduleName, err)
				failureCount++
				continue
			}

			successCount++
			fmt.Printf("Created schedule %d: %s at %s\n", scheduleCount, scheduleName, scheduleTime.Format("15:04:05"))
		}
	}

	fmt.Printf("Schedule creation complete: %d total, %d successful, %d failed\n", scheduleCount, successCount, failureCount)
}

func createSchedule(ctx context.Context, client *scheduler.Client, name string, scheduleTime time.Time, sqsMessage string, roleArn string) error {
	// Convert time to EventBridge schedule expression
	scheduleExpression := fmt.Sprintf("at(%s)", scheduleTime.UTC().Format("2006-01-02T15:04:05"))

	input := &scheduler.CreateScheduleInput{
		Name:               &name,
		ScheduleExpression: &scheduleExpression,
		Target: &types.Target{
			Arn:     &[]string{"arn:aws:scheduler:::aws-sdk:sqs:sendMessage"}[0],
			RoleArn: &roleArn,
			Input:   &sqsMessage,
		},
		FlexibleTimeWindow: &types.FlexibleTimeWindow{
			Mode: types.FlexibleTimeWindowModeOff,
		},
	}

	_, err := client.CreateSchedule(ctx, input)
	return err
}
