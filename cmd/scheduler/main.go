package main

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"time"

	"github.com/aws/aws-sdk-go-v2/service/scheduler"
	"github.com/aws/aws-sdk-go-v2/service/scheduler/types"
	"sqs-test/config"
	"sqs-test/utils"
)

func main() {
	fmt.Println("Starting EventBridge Scheduler Client...")

	// Load configuration
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// Create scheduler client
	schedulerClient := scheduler.NewFromConfig(cfg.AWSConfig)

	// Calculate start time (15 seconds from now)
	startTime := time.Now().Add(15 * time.Second)
	fmt.Printf("Scheduling 100 messages starting at: %s\n", startTime.Format("15:04:05"))

	// Create 100 schedules: 10 per second for 10 seconds
	scheduleCount := 0
	for second := 0; second < 10; second++ {
		scheduleTime := startTime.Add(time.Duration(second) * time.Second)
		
		for messageInSecond := 0; messageInSecond < 10; messageInSecond++ {
			scheduleCount++
			scheduleName := fmt.Sprintf("sqs-test-schedule-%d", scheduleCount)
			
			// Generate unique message
			messageBody := utils.GenerateUniqueMessage()
			
			// Create SQS message payload
			sqsMessage := map[string]interface{}{
				"QueueUrl": cfg.SQSQueueURL,
				"MessageBody": messageBody,
			}
			
			sqsMessageJSON, err := json.Marshal(sqsMessage)
			if err != nil {
				log.Printf("Failed to marshal SQS message: %v", err)
				continue
			}

			// Create schedule
			err = createSchedule(schedulerClient, scheduleName, scheduleTime, string(sqsMessageJSON), cfg.SchedulerRoleARN)
			if err != nil {
				log.Printf("Failed to create schedule %s: %v", scheduleName, err)
				continue
			}
			
			fmt.Printf("Created schedule %d: %s at %s\n", scheduleCount, scheduleName, scheduleTime.Format("15:04:05"))
		}
	}

	fmt.Printf("Successfully created %d schedules\n", scheduleCount)
}

func createSchedule(client *scheduler.Client, name string, scheduleTime time.Time, sqsMessage string, roleArn string) error {
	// Convert time to EventBridge schedule expression
	scheduleExpression := fmt.Sprintf("at(%s)", scheduleTime.UTC().Format("2006-01-02T15:04:05"))
	
	input := &scheduler.CreateScheduleInput{
		Name:               &name,
		ScheduleExpression: &scheduleExpression,
		Target: &types.Target{
			Arn:     &[]string{"arn:aws:scheduler:::aws-sdk:sqs:sendMessage"}[0],
			RoleArn: &roleArn,
			Input:   &sqsMessage,
		},
		FlexibleTimeWindow: &types.FlexibleTimeWindow{
			Mode: types.FlexibleTimeWindowModeOff,
		},
	}

	_, err := client.CreateSchedule(context.TODO(), input)
	return err
}
