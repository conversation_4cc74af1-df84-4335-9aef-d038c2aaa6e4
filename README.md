# AWS SQS EventBridge Scheduler Experiment

This project implements an AWS SQS experiment with EventBridge Scheduler for testing message processing and deduplication.

## Components

### 1. EventBridge Scheduler Client (`cmd/scheduler/main.go`)
- Creates 100 individual one-time schedules
- Schedules 10 messages per second for 10 seconds
- Starts 15 seconds after execution
- Each message contains a unique UUID

### 2. SQS Consumer Client (`cmd/consumer/main.go`)
- Distributed-ready SQS message consumer
- Tracks processed messages and duplicates using MD5 hashing
- Outputs metrics to console every 5 seconds
- Supports graceful shutdown with Ctrl+C

## Setup

### 1. Environment Configuration
Copy and update the `.env` file with your AWS resources:

```bash
# AWS Configuration
AWS_PROFILE=viewbid-dev
AWS_REGION=us-east-1

# SQS Configuration
SQS_QUEUE_URL=https://sqs.us-east-1.amazonaws.com/YOUR_ACCOUNT/YOUR_QUEUE_NAME

# EventBridge Scheduler Configuration
SCHEDULER_ROLE_ARN=arn:aws:iam::YOUR_ACCOUNT:role/EventBridgeSchedulerRole
```

### 2. AWS Prerequisites
- AWS CLI configured with profile `viewbid-dev`
- SQS queue created
- IAM role for EventBridge Scheduler with permissions to send messages to SQS

### 3. Build Applications
```bash
# Build scheduler
go build -o bin/scheduler cmd/scheduler/main.go

# Build consumer
go build -o bin/consumer cmd/consumer/main.go
```

## Usage

### 1. Start Consumer(s)
Run one or more consumer instances:
```bash
./bin/consumer
```

### 2. Run Scheduler
Execute the scheduler to create schedules:
```bash
./bin/scheduler
```

The scheduler will create 100 schedules starting 15 seconds after execution.

## Expected Behavior

1. **Scheduler**: Creates 100 EventBridge schedules that will trigger over 10 seconds
2. **Consumer**: Processes messages and reports:
   - Total messages processed
   - Unique messages
   - Duplicate messages (based on MD5 hash)

## Metrics Output

Consumer outputs metrics every 5 seconds:
```
[15:04:05] Total: 45, Unique: 45, Duplicates: 0
[15:04:10] Total: 78, Unique: 78, Duplicates: 0
```

## Distributed Testing

Run multiple consumer instances to test distributed processing:
```bash
# Terminal 1
./bin/consumer

# Terminal 2  
./bin/consumer

# Terminal 3
./bin/consumer
```

Each instance will process messages independently and track its own metrics.
