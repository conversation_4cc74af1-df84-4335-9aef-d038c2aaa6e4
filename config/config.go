package config

import (
	"context"
	"fmt"
	"os"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/joho/godotenv"
)

type Config struct {
	AWSProfile      string
	AWSRegion       string
	SQSQueueURL     string
	SchedulerRoleARN string
	AWSConfig       aws.Config
}

func Load() (*Config, error) {
	// Load .env file
	if err := godotenv.Load(); err != nil {
		fmt.Printf("Warning: .env file not found: %v\n", err)
	}

	cfg := &Config{
		AWSProfile:      getEnv("AWS_PROFILE", "viewbid-dev"),
		AWSRegion:       getEnv("AWS_REGION", "us-east-1"),
		SQSQueueURL:     getEnv("SQS_QUEUE_URL", ""),
		SchedulerRoleARN: getEnv("SCHEDULER_ROLE_ARN", ""),
	}

	// Validate required fields
	if cfg.SQSQueueURL == "" {
		return nil, fmt.Errorf("SQS_QUEUE_URL is required")
	}
	if cfg.SchedulerRoleARN == "" {
		return nil, fmt.Errorf("SCHEDULER_ROLE_ARN is required")
	}

	// Load AWS config with profile
	awsCfg, err := config.LoadDefaultConfig(context.TODO(),
		config.WithSharedConfigProfile(cfg.AWSProfile),
		config.WithRegion(cfg.AWSRegion),
	)
	if err != nil {
		return nil, fmt.Errorf("failed to load AWS config: %w", err)
	}
	cfg.AWSConfig = awsCfg

	return cfg, nil
}

func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}
