package utils

import (
	"crypto/md5"
	"fmt"
	"sync"
	"time"

	"github.com/google/uuid"
)

// GenerateUniqueMessage generates a unique message with UUID
func GenerateUniqueMessage() string {
	return uuid.New().String()
}

// CalculateMD5 calculates MD5 hash of a string
func CalculateMD5(text string) string {
	hash := md5.Sum([]byte(text))
	return fmt.Sprintf("%x", hash)
}

// MessageTracker tracks processed messages and duplicates
type MessageTracker struct {
	mu              sync.RWMutex
	processedHashes map[string]bool
	totalProcessed  int
	duplicateCount  int
}

// NewMessageTracker creates a new message tracker
func NewMessageTracker() *MessageTracker {
	return &MessageTracker{
		processedHashes: make(map[string]bool),
	}
}

// ProcessMessage processes a message and returns if it's a duplicate
func (mt *MessageTracker) ProcessMessage(messageBody string) bool {
	hash := CalculateMD5(messageBody)
	
	mt.mu.Lock()
	defer mt.mu.Unlock()
	
	mt.totalProcessed++
	
	if mt.processedHashes[hash] {
		mt.duplicateCount++
		return true // is duplicate
	}
	
	mt.processedHashes[hash] = true
	return false // not duplicate
}

// GetStats returns current statistics
func (mt *MessageTracker) GetStats() (total int, duplicates int, unique int) {
	mt.mu.RLock()
	defer mt.mu.RUnlock()
	
	return mt.totalProcessed, mt.duplicateCount, mt.totalProcessed - mt.duplicateCount
}

// PrintStats prints current statistics to console
func (mt *MessageTracker) PrintStats() {
	total, duplicates, unique := mt.GetStats()
	fmt.Printf("[%s] Total: %d, Unique: %d, Duplicates: %d\n", 
		time.Now().Format("15:04:05"), total, unique, duplicates)
}
