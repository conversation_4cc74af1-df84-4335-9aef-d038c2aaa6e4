package utils

import (
	"fmt"
	"sync"
	"time"

	"github.com/google/uuid"
)

// GenerateUniqueMessage generates a unique message with UUID
func GenerateUniqueMessage() string {
	return uuid.New().String()
}

// MessageTracker tracks processed messages and duplicates
type MessageTracker struct {
	mu              sync.RWMutex
	processedMsgIDs map[string]bool
	totalProcessed  int
	duplicateCount  int
}

// NewMessageTracker creates a new message tracker
func NewMessageTracker() *MessageTracker {
	return &MessageTracker{
		processedMsgIDs: make(map[string]bool),
	}
}

// ProcessMessage processes a message and returns if it's a duplicate
func (mt *MessageTracker) ProcessMessage(messageID string) bool {
	mt.mu.Lock()
	defer mt.mu.Unlock()

	mt.totalProcessed++

	if mt.processedMsgIDs[messageID] {
		mt.duplicateCount++
		return true // is duplicate
	}

	mt.processedMsgIDs[messageID] = true
	return false // not duplicate
}

// GetStats returns current statistics
func (mt *MessageTracker) GetStats() (total int, duplicates int, unique int) {
	mt.mu.RLock()
	defer mt.mu.RUnlock()

	return mt.totalProcessed, mt.duplicateCount, mt.totalProcessed - mt.duplicateCount
}

// PrintStats prints current statistics to console
func (mt *MessageTracker) PrintStats() {
	total, duplicates, unique := mt.GetStats()
	fmt.Printf("[%s] Total: %d, Unique: %d, Duplicates: %d\n",
		time.Now().Format("15:04:05"), total, unique, duplicates)
}
